config:
  service: transporte-gkv-dispatchgkv
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 1
  task_config:
    cpu: "256"
    memory: "512"
    containerDefinitions:
      - name: app
        image: ${DISPATCHGKV_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            "awslogs-group": "transporte-gkv-dispatchgkv"
            "awslogs-region": "us-east-1"
            "awslogs-stream-prefix": "transporte-gkv-dispatchgkv"
        portMappings:
          - containerPort: 80   # NGINX escucha aquí (para el ALB)
          - containerPort: 3739 # Servicio DispatchGKV escucha aquí (NGINX hace proxy)
          - containerPort: 8080 # Puerto para compatibilidad con ALB existente
        healthCheck:
          command: ["CMD-SHELL", "curl -f http://localhost/health || exit 1"]
          interval: 30
          timeout: 5
          retries: 3
          startPeriod: 60
    

