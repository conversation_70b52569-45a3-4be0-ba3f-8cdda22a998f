config:
  service: transporte-gkv-dispatchack
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 1
  task_config:
    cpu: "256"
    memory: "512"
    containerDefinitions:
      - name: app
        image: ${DISPATCHACK_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            "awslogs-group": "transporte-gkv-dispatchack"
            "awslogs-region": "us-east-1"
            "awslogs-stream-prefix": "transporte-gkv-dispatchack"
        portMappings:
          - containerPort: 80   # NGINX escucha aquí (para el ALB)
          - containerPort: 3738 # Servicio DispatchACK escucha aquí (NGINX hace proxy)
          - containerPort: 8080 # Puerto para compatibilidad con ALB existente
        healthCheck:
          command: ["CMD-SHELL", "curl -f http://localhost/health || exit 1"]
          interval: 30
          timeout: 5
          retries: 3
          startPeriod: 60
    

