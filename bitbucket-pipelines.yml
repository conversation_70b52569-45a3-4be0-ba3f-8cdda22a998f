# Pipeline específico para servicios GKV con Dockerfiles individuales
image: atlassian/default-image:2
clone:
  depth: full

definitions:
  script_build: &scripts_build
    name: build image with specific Dockerfile
    oidc: true
    script:
      - export AWS_WEB_IDENTITY_TOKEN_FILE="/tmp/aws-web-identity-token-file"
      - echo ${BITBUCKET_STEP_OIDC_TOKEN} > ${AWS_WEB_IDENTITY_TOKEN_FILE}
      - test -f dotenv && source dotenv
      - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
      - unzip awscliv2.zip
      - ./aws/install
      - eval $(aws sts assume-role --role-arn ${EXECUTE_ROLE_ARN} --role-session-name test | jq -r '.Credentials | "export AWS_ACCESS_KEY_ID=\(.AccessKeyId)\nexport AWS_SECRET_ACCESS_KEY=\(.SecretAccessKey)\nexport AWS_SESSION_TOKEN=\(.SessionToken)\n"')
      - env
      - export SEMVER=$(docker run --rm -v $BITBUCKET_CLONE_DIR:/repo gittools/gitversion:5.12.0-alpine.3.14-6.0 /repo -output json -showvariable FullSemVer)
      - echo ${IMAGE_NAME}:${SEMVER}
      - apt-get update && apt-get install -y build-essential clang make curl libcurl4-openssl-dev libmysqlclient-dev
      - envsubst < ./transporte_enterprise/TRANSPORT_MASTER_SOURCE/Test/conf/params.template > ./transporte_enterprise/TRANSPORT_MASTER_SOURCE/Test/conf/params.cfg
      - (cd transporte_enterprise/TRANSPORT_MASTER_SOURCE/${APP_FOLDER} && make clean all)
      - (cd transporte_enterprise/TRANSPORT_MASTER_SOURCE && docker build -f ${APP_FOLDER}/Dockerfile.${APP_FOLDER} -t ${IMAGE_NAME}:${SEMVER} .)
      - docker run --rm ${IMAGE_NAME}:${SEMVER} ls -la /start.sh || echo "Error al verificar /start.sh"
      - docker run --rm ${IMAGE_NAME}:${SEMVER} cat /start.sh || echo "Error al leer /start.sh"
      - pipe: atlassian/aws-ecr-push-image:2.5.0
        variables:
          AWS_DEFAULT_REGION: ${ECR_REGION}
          AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
          AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
          AWS_SESSION_TOKEN: ${AWS_SESSION_TOKEN}
          IMAGE_NAME: ${IMAGE_NAME}
          TAGS: $SEMVER
          DEBUG: "true"
          
    services:
      - docker
    caches:
      - docker

  build_dev: &build_push_dev
    name: build image
    oidc: true
    deployment: Develop
    <<: *scripts_build

  build_prod: &build_push_prd
    name: build image
    oidc: true
    deployment: Production
    <<: *scripts_build

  update_ecs_service: &update_ecs_service
    name: update ecs task
    image: atlassian/default-image:4
    oidc: true
    script:
      - test -f dotenv && source dotenv
      - bash scripts/update-task.sh

      # Generador de variables para build
  generate_build_vars_dev: &generate_build_vars_dev
    step:
      name: generate variables
      artifacts:
        - dotenv
      script:
        - echo "export IMAGE_NAME=transporte-gkv-${API_NAME} APP_FOLDER=${API_FOLDER} ECR_REGION=us-east-1 AWS_ROLE_ARN=arn:aws:iam::227932815557:role/bitbucket-openid EXECUTE_ROLE_ARN=arn:aws:iam::555456986164:role/deployer" > dotenv
  
  generate_build_vars_prd: &generate_build_vars_prd
    step:
      name: generate variables
      artifacts:
        - dotenv
      script:
        - echo "export IMAGE_NAME=transporte-gkv-${API_NAME} APP_FOLDER=${API_FOLDER} ECR_REGION=us-east-1 AWS_ROLE_ARN=arn:aws:iam::227932815557:role/bitbucket-openid EXECUTE_ROLE_ARN=arn:aws:iam::227932815557:role/deployer" > dotenv
    
  # Generador de variables para update
  generate_update_vars: &generate_update_vars
    step:
      name: generate variables
      services:
        - docker
      caches:
        - docker
      script: 
        - export SEMVER=$(docker run --rm -v $BITBUCKET_CLONE_DIR:/repo gittools/gitversion:5.12.0-alpine.3.14-6.0 /repo -output json -showvariable FullSemVer)
        - echo "export IMAGE_TAG=${SEMVER}" > dotenv
        - cat scripts/envs/smscorp/${env}.env scripts/envs/smscorp/main.env >> dotenv
        - cp scripts/taskdef/transporte-gkv-${API_NAME}-${env}.yml task-template.yml
        - cp scripts/clusters/${env}-smscorp-ecs-cluster.yml cluster-config.yml
      artifacts:
        - dotenv
        - cluster-config.yml
        - task-template.yml

  

pipelines:
  custom:
    build_dev:
      - variables:
          - name: API_NAME
            default: "dispatchack"
            allowed-values:
              - dispatchack
              - dispatchgkv
              - dispatchmo
              - listengkv
              - maintenance
          - name: API_FOLDER
            default: "DispatchACK"
            allowed-values:
                - DispatchACK
                - DispatchGKV
                - DispatchMO
                - ListenGKV
                - Maintenance
      - <<: *generate_build_vars_dev
      - step: *build_push_dev

       # BUILD PIPELINES PRD
    build_prd:
      - variables:
          - name: API_NAME
            default: "dispatchack"
            allowed-values:
              - dispatchack
              - dispatchgkv
              - dispatchmo
              - listengkv
              - maintenance
          - name: API_FOLDER
            default: "DispatchACK"
            allowed-values:
                - DispatchACK
                - DispatchGKV
                - DispatchMO
                - ListenGKV
                - Maintenance
      - <<: *generate_build_vars_prd
      - step: *build_push_prd

       # UPDATE PIPELINES
    update-api:
      - variables:
        - name: env
          default: dev
          allowed-values:
            - dev
            - prd
        - name: API_NAME
          default: "dispatchack"
          allowed-values:
              - dispatchack
              - dispatchgkv
              - dispatchmo
              - listengkv
              - maintenance
        - name: API_FOLDER
          default: "DispatchACK"
          allowed-values:
              - DispatchACK
              - DispatchGKV
              - DispatchMO
              - ListenGKV
              - Maintenance
      - <<: *generate_update_vars
      - step: *update_ecs_service
    
    
