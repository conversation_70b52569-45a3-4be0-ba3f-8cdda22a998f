######################################
# Important: Must be an ASCII file
# ' and " are allowed to enclose text
######################################

# Common
APP_DBHOST            = ${APP_DBHOST}
APP_DBNAME            = ${APP_DBNAME}
APP_DBUSER            = ${APP_DBUSER}
APP_DBPASS            = ${APP_DBPASS}
APP_DBPORT            = 3306
APP_PREFIX            = ENT_

# LGK
LGK_IP                = ${LGK_IP}
LGK_PORT              = 3741
LGK_LMO_REQUEST_URI   = /lws_demo_mx/lmo
LGK_ACK_REQUEST_URI   = /lws_demo_mx/ack
LGK_RMT_REQUEST_URI   = /lws_demo_mx/rmt
LGK_SP_UPDATE_MT      = sp_transport_upd_trafficMT
LGK_SP_INSERT_MO      = sp_transport_ins_trafficMO
LGK_SP_INSERT_ACK     = sp_transport_ins_acknowledgeMT
LGK_MAX_SRV           = 5
LGK_POLL_TIME         = 5
LGK_SHOW_WAIT         = 60

# DGK
DGK_LAWS_URL          = ${DGK_LAWS_URL}
DGK_THREADS_COUNT     = 1
DGK_SP_STATUS_MT      = sp_transport_sel_companies_for_trafficMT
DGK_SP_SELECT_MT      = sp_transport_sel_trafficMT
DGK_SP_UPDATE_MT      = sp_transport_upd_trafficMT
DGK_INACTIVE_TIME     = 90
DGK_READ_WAIT         = 5
DGK_SHOW_WAIT         = 30
DGK_BATCH_SIZE        = 200
DGK_BATCH_RPS         = 5 

# DMO
DMO_THREADS_COUNT     = 2
DMO_SP_STATUS_MO      = sp_transport_sel_companies_for_trafficMO
DMO_SP_SELECT_MO      = sp_transport_sel_trafficMO
DMO_SP_UPDATE_MO      = sp_transport_upd_trafficMO
DMO_USER              = ${DMO_USER}
DMO_PASS              = ${DMO_PASS}
DMO_INACTIVE_TIME     = 90
DMO_READ_WAIT         = 5
DMO_SHOW_WAIT         = 30
DMO_BATCH_SIZE        = 200
DMO_BATCH_RPS         = 5 

# DACK
DACK_THREADS_COUNT    = 1
DACK_SP_STATUS_ACK    = sp_transport_sel_companies_for_acknowledgeMT
DACK_SP_SELECT_ACK    = sp_transport_sel_acknowledgeMT
DACK_SP_UPDATE_ACK    = sp_transport_upd_acknowledgeMT
DACK_USER             = ${DACK_USER}
DACK_PASS             = ${DACK_PASS}
DACK_INACTIVE_TIME    = 90
DACK_READ_WAIT        = 5
DACK_SHOW_WAIT        = 30
DACK_BATCH_SIZE       = 200
DACK_BATCH_RPS        = 5 

# MSD
MSD_READ_WAIT         = 5
MSD_SHOW_WAIT         = 60
MSD_BATCH_SIZE        = 100
MSD_POST_TIME         = 600
MSD_SP_BLACKLIST      = sp_transport_process_blacklist
MSD_SP_START_CAMPAIGN = sp_transport_start_campaign
MSD_SP_MOVE_CAMPAIGN  = sp_transport_move_campaign_to_trafic_mt
MSD_SP_END_CAMPAIGN   = sp_transport_end_campaign
MSD_SP_POST_CAMPAIGN  = sp_transport_post_end_campaign

############# EOF #############