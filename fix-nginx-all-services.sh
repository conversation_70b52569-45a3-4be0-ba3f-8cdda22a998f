#!/bin/bash

echo "🔧 Aplicando configuración de nginx correcta a todos los servicios..."

# Función para actualizar la configuración de nginx en un Dockerfile
fix_nginx_config() {
    local service=$1
    local dockerfile=$2
    local port=$3
    
    echo "📦 Actualizando configuración de nginx para $service (puerto $port)..."
    
    # Buscar la línea que crea el nginx.conf y reemplazarla con la configuración correcta
    sed -i '/RUN echo.*worker_processes.*nginx.conf/c\
RUN echo '\''worker_processes  1;'\'' > /etc/nginx/nginx.conf && \\\
    echo '\''events { worker_connections  1024; }'\'' >> /etc/nginx/nginx.conf && \\\
    echo '\''http {'\'' >> /etc/nginx/nginx.conf && \\\
    echo '\''    include       /etc/nginx/mime.types;'\'' >> /etc/nginx/nginx.conf && \\\
    echo '\''    default_type  application/octet-stream;'\'' >> /etc/nginx/nginx.conf && \\\
    echo '\''    sendfile        on;'\'' >> /etc/nginx/nginx.conf && \\\
    echo '\''    keepalive_timeout  65;'\'' >> /etc/nginx/nginx.conf && \\\
    echo '\''    include /etc/nginx/conf.d/*.conf;'\'' >> /etc/nginx/nginx.conf && \\\
    echo '\''}'\'' >> /etc/nginx/nginx.conf' "$dockerfile"
    
    echo "✅ $service nginx configurado correctamente"
}

# Aplicar la corrección a todos los servicios excepto ListenGKV (que ya funciona)
echo "🚀 Iniciando corrección de nginx..."

fix_nginx_config "DispatchGKV" "transporte_enterprise/TRANSPORT_MASTER_SOURCE/DispatchGKV/Dockerfile.DispatchGKV" "3739"
fix_nginx_config "DispatchMO" "transporte_enterprise/TRANSPORT_MASTER_SOURCE/DispatchMO/Dockerfile.DispatchMO" "3740"
fix_nginx_config "DispatchACK" "transporte_enterprise/TRANSPORT_MASTER_SOURCE/DispatchACK/Dockerfile.DispatchACK" "3741"
fix_nginx_config "Maintenance" "transporte_enterprise/TRANSPORT_MASTER_SOURCE/Maintenance/Dockerfile.Maintenance" "3742"

echo ""
echo "🎉 ¡Configuración de nginx aplicada a todos los servicios!"
echo ""
echo "📋 Próximos pasos:"
echo "1. Reconstruir las imágenes: ./build-all-services.sh"
echo "2. Probar todos los servicios: ./test-all-services.sh"
echo ""
echo "💡 Después de esto, todos los servicios deberían funcionar correctamente."
