#!/bin/bash

echo "🚀 Reconstruyendo servicios restantes con configuración corregida..."

# Función para reconstruir un servicio
rebuild_service() {
    local service=$1
    local dockerfile=$2
    local tag=$3
    
    echo ""
    echo "🔨 Reconstruyendo $service..."
    
    if docker build --no-cache -f "transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/$dockerfile" -t "$tag" "transporte_enterprise/TRANSPORT_MASTER_SOURCE/"; then
        echo "✅ $service reconstruido exitosamente"
    else
        echo "❌ Error reconstruyendo $service"
        return 1
    fi
}

# Reconstruir servicios restantes
echo "📦 Iniciando reconstrucción..."

rebuild_service "DispatchMO" "Dockerfile.DispatchMO" "gkv-dispatchmo:latest"
rebuild_service "DispatchACK" "Dockerfile.DispatchACK" "gkv-dispatchack:latest"
rebuild_service "Maintenance" "Dockerfile.Maintenance" "gkv-maintenance:latest"

echo ""
echo "🎉 ¡Reconstrucción completada!"
echo ""
echo "💡 Ahora todos los servicios deberían funcionar correctamente."
